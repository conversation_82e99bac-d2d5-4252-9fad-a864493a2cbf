import httpx
import json
import time
import random
import uuid


# 配置区域 - 需要更新的参数
COOKIE = "hook_slardar_session_id=2025073017592839920BEBA2D613EF2863,ttwid=1%7CiMFHx1Da_BetezZn05zfft9t1j-kjns3qTR_5L6OV08%7C1753869568%7Ca4235a632c824c9806738adf2a81d8f6c10d14bc68364c986d1de738467b554c; _ga=GA1.1.*********.1753869579; ttcid=680f850c9d6c4cef8c2896d7eb66eabc38; s_v_web_id=verify_mdpso4wy_jIHunFlc_ubrJ_4rnM_AFJ2_lnMQE6tXGixX; passport_csrf_token=d24a324fc83e183d82d2ff376865d10a; passport_csrf_token_default=d24a324fc83e183d82d2ff376865d10a; passport_mfa_token=CjhmMmxq74s45voqX5p6YdkTf1aOmqKOzpFl16J07%2BVgDkKOQQEZLdf9yskI1i%2BDekA5EUy5DoOLVBpKCjwAAAAAAAAAAAAAT0vzn5wH%2F%2BIeVMweyqaIHWGbns9B9a58PfbsvTRkyotTHxZwlgjMUlSxPv1wGWBuNS0Q1474DRj2sdFsIAIiAQOsujFr; d_ticket=560ba6c243be3cc7d1a5ef686b58aeaac2adc; odin_tt=92daead88f19c6eeeddec32e138fe8b384de6e89757f7daecc7e9047933659c6fbd9ccfa1aaf65e89782b55c3b05ebc374fbbb39e4a7380f03ee65be5f2b5b7b; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=30b6a10d87ae77cce375cafda8fc3f2a%2C; passport_auth_status_ss=30b6a10d87ae77cce375cafda8fc3f2a%2C; sid_guard=76f286fcb74fe1c863d95e984c75a39e%7C1753869599%7C5184000%7CSun%2C+28-Sep-2025+09%3A59%3A59+GMT; uid_tt=f0649b32573486f603a5910e87efee82; uid_tt_ss=f0649b32573486f603a5910e87efee82; sid_tt=76f286fcb74fe1c863d95e984c75a39e; sessionid=76f286fcb74fe1c863d95e984c75a39e; sessionid_ss=76f286fcb74fe1c863d95e984c75a39e; session_tlb_tag=sttt%7C8%7CdvKG_LdP4chj2V6YTHWjnv_________P3rzelBNJ06HunzJflrwKP1i5fPvlQLpG3XaOLaXNUxU%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; ssid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC45NTM1NTE4Nzg2NjM2NzQyfQ==.+VUnRvmf8t8OAT5e64H0M4TT/JD6eqpFNXin/KSDCaQ=; i18next=zh; flow_ssr_sidebar_expand=1; ttwid=1%7CG_ugBSA7aPKN7hVyth9pnegG5dZcVXYm0c59Fglz-OY%7C1753885322%7Cf2af41accef6951c94442e89c340cbcfa9d7ae4168dc231cd0f9d3937cd9af70; passport_fe_beating_status=true; msToken=DA1QpP7aquBoqZApC0XwDQqLxAXqH22TKQ-sWbTXR2o8Rgwmv5Dp4B9vdNTahsYdDJyiLUXxlkn34O5jT1OHRitDSH2v9dOD7pc4G8IVlhFURuaxDW3s87UGU8bIl7zt18ODuv64xSmD; flow_user_country=CN; tt_scid=NsGZSlIPaqR8-nyNIGLjSEKIadRXi7V1OgR5VAGFAOn8IkrbZnfmX.vPbuZKH7Kda78a; _ga_G8EP5CG8VZ=GS2.1.s1753885333$o2$g1$t1753885337$j56$l0$h0"


# 反限流策略类
class AntiRateLimit:
    def __init__(self):
        self.last_request_time = 0
        self.request_count = 0
        self.conversation_id = "0"  # 新会话
        self.local_conversation_id = f"local_{int(time.time() * 1000)}{random.randint(1000, 9999)}"
        self.session_start_time = time.time()
        
    def generate_message_id(self):
        """生成消息ID"""
        return str(uuid.uuid4())
        
    def generate_device_id(self):
        """生成设备ID - 保持会话期间一致"""
        if not hasattr(self, '_device_id'):
            self._device_id = str(random.randint(7000000000000000000, 7999999999999999999))
        return self._device_id
        
    def generate_flow_trace(self):
        """生成flow trace"""
        part1 = ''.join(random.choices('0123456789abcdef', k=32))
        part2 = ''.join(random.choices('0123456789abcdef', k=16))
        return f"04-{part1}-{part2}-01"
        
    def human_like_delay(self):
        """模拟人类行为延迟"""
        current_time = time.time()
        time_diff = current_time - self.last_request_time
        
        # 基础延迟：3-10秒
        base_delay = random.uniform(3, 10)
        
        # 如果请求太频繁，增加延迟
        if self.request_count > 0 and time_diff < 5:
            extra_delay = random.uniform(5, 15)
            base_delay += extra_delay
            print(f"🛡️ 检测到频繁请求，增加延迟 {extra_delay:.1f} 秒")
        
        # 随机"思考"时间
        if random.random() < 0.3:  # 30%概率
            think_time = random.uniform(2, 8)
            base_delay += think_time
            print(f"🤔 模拟思考时间 {think_time:.1f} 秒")
        
        if time_diff < base_delay:
            wait_time = base_delay - time_diff
            print(f"⏰ 人性化延迟 {wait_time:.1f} 秒...")
            time.sleep(wait_time)
            
        self.last_request_time = time.time()
        self.request_count += 1

# 全局反限流实例
anti_limit = AntiRateLimit()


def send_chat_message_anti_limit(message: str, cookie: str = None):
    """发送聊天消息 - 反限流版本"""
    
    # 应用人性化延迟
    anti_limit.human_like_delay()
    
    print(f"🚀 发送第 {anti_limit.request_count} 条消息...")
    
    url = "https://www.doubao.com/samantha/chat/completion"
    
    # 动态参数
    device_id = anti_limit.generate_device_id()
    web_id = device_id
    
    # URL参数 - 完整版本，模拟真实浏览器
    params = {
        "aid": "497858",
        "device_id": device_id,
        "device_platform": "web", 
        "language": "zh",
        "pc_version": "2.29.3",
        "pkg_type": "release_version",
        "real_aid": "497858",
        "region": "CN",
        "samantha_web": "1",
        "sys_region": "CN",
        "tea_uuid": web_id,
        "use-olympus-account": "1",
        "version_code": "20800",
        "web_id": web_id
    }
    
    # 请求头 - 完整版本，模拟真实浏览器
    headers = {
        "Host": "www.doubao.com",
        "Connection": "keep-alive",
        "x-flow-trace": anti_limit.generate_flow_trace(),
        "sec-ch-ua-platform": '"Linux"',
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "Agw-Js-Conv": "str, str",
        "last-event-id": "undefined",
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/138.0.3351.83",
        "content-type": "application/json",
        "Accept": "*/*",
        "Origin": "https://www.doubao.com",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Referer": "https://www.doubao.com/chat/",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cookie": cookie or COOKIE
    }
    
    # 请求体 - 完整版本，包含会话信息
    payload = {
        "messages": [
            {
                "content": json.dumps({"text": message}),
                "content_type": 2001,
                "attachments": [],
                "references": []
            }
        ],
        "completion_option": {
            "is_regen": False,
            "with_suggest": True,
            "need_create_conversation": anti_limit.conversation_id == "0",
            "launch_stage": 1,
            "is_replace": False,
            "is_delete": False,
            "message_from": 0,
            "use_deep_think": False,
            "use_auto_cot": True,
            "resend_for_regen": False,
            "event_id": "0"
        },
        "evaluate_option": {
            "web_ab_params": ""
        },
        "conversation_id": anti_limit.conversation_id,
        "local_conversation_id": anti_limit.local_conversation_id,
        "local_message_id": anti_limit.generate_message_id()
    }
    
    # 发送请求
    try:
        with httpx.Client(timeout=30.0) as client:
            # 先发送普通请求检查状态
            response = client.post(url, params=params, headers=headers, json=payload)
            print(f"📡 响应状态: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
            
            # 重新发送流式请求
            with client.stream("POST", url, params=params, headers=headers, json=payload) as stream_response:
                if stream_response.status_code != 200:
                    print(f"❌ 流式请求失败: {stream_response.status_code}")
                    return None
                
                print("📺 接收流式响应...")
                full_text = ""
                
                for line in stream_response.iter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]
                        
                        if data_str.strip() == "":
                            continue
                            
                        try:
                            data = json.loads(data_str)
                            event_data = data.get("event_data", "{}")
                            event_type = data.get("event_type")
                            
                            # 检查错误
                            if event_type == 2005:
                                try:
                                    error_obj = json.loads(event_data)
                                    error_code = error_obj.get("code")
                                    error_message = error_obj.get("message", "")
                                    
                                    if error_code == 710022004:
                                        print("⚠️ 仍然被限流，需要更长的延迟...")
                                        return None
                                    else:
                                        print(f"❌ 服务器错误: {error_message} (代码: {error_code})")
                                        return None
                                except:
                                    print("❌ 服务器返回错误")
                                    return None
                            
                            # 处理消息
                            elif event_type == 2001:
                                event_obj = json.loads(event_data)
                                message_data = event_obj.get("message", {})
                                content = message_data.get("content", "{}")
                                
                                if content != "{}":
                                    content_obj = json.loads(content)
                                    text = content_obj.get("text", "")
                                    if text:
                                        print(text, end="", flush=True)
                                        full_text += text
                                        
                        except json.JSONDecodeError:
                            continue
                
                print()  # 换行
                return full_text
                
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None


def interactive_chat_anti_limit():
    """交互式聊天 - 反限流版本"""
    print("🤖 豆包聊天模式 - 反限流版本")
    print("💡 自动应用人性化延迟策略")
    print("📝 输入 'quit' 退出")
    print("-" * 50)
    
    while True:
        try:
            message = input("\n您: ").strip()
            if message.lower() in ['quit', 'exit', '退出']:
                print("👋 再见!")
                break
            
            if not message:
                continue
                
            print("豆包: ", end="", flush=True)
            response = send_chat_message_anti_limit(message)
            
            if not response:
                print("❌ 请求失败")
                choice = input("是否继续尝试? (y/n): ")
                if choice.lower() != 'y':
                    break
                    
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break


if __name__ == "__main__":
    print("🛡️ 豆包聊天API - 反限流版本")
    print("=" * 50)
    print("🔧 反限流策略:")
    print("  - 人性化延迟 (3-10秒基础 + 随机思考时间)")
    print("  - 动态参数生成")
    print("  - 完整浏览器模拟")
    print("  - 会话状态维护")
    print()
    
    # 测试单条消息
    test_message = "你好，请介绍一下自己"
    print(f"🧪 测试消息: {test_message}")
    print("-" * 30)
    
    response = send_chat_message_anti_limit(test_message)
    
    if response:
        print(f"\n✅ 成功! 回复长度: {len(response)} 字符")
        print("\n💬 可以调用 interactive_chat_anti_limit() 开始连续对话")
    else:
        print("\n❌ 测试失败，可能需要调整策略")
