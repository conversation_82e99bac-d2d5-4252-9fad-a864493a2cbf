import httpx
import json
import time
import random
import uuid
import hashlib


# 配置区域
COOKIE = "hook_slardar_session_id=2025073017592839920BEBA2D613EF2863,ttwid=1%7CiMFHx1Da_BetezZn05zfft9t1j-kjns3qTR_5L6OV08%7C1753869568%7Ca4235a632c824c9806738adf2a81d8f6c10d14bc68364c986d1de738467b554c; _ga=GA1.1.*********.1753869579; ttcid=680f850c9d6c4cef8c2896d7eb66eabc38; s_v_web_id=verify_mdpso4wy_jIHunFlc_ubrJ_4rnM_AFJ2_lnMQE6tXGixX; passport_csrf_token=d24a324fc83e183d82d2ff376865d10a; passport_csrf_token_default=d24a324fc83e183d82d2ff376865d10a; passport_mfa_token=CjhmMmxq74s45voqX5p6YdkTf1aOmqKOzpFl16J07%2BVgDkKOQQEZLdf9yskI1i%2BDekA5EUy5DoOLVBpKCjwAAAAAAAAAAAAAT0vzn5wH%2F%2BIeVMweyqaIHWGbns9B9a58PfbsvTRkyotTHxZwlgjMUlSxPv1wGWBuNS0Q1474DRj2sdFsIAIiAQOsujFr; d_ticket=560ba6c243be3cc7d1a5ef686b58aeaac2adc; odin_tt=92daead88f19c6eeeddec32e138fe8b384de6e89757f7daecc7e9047933659c6fbd9ccfa1aaf65e89782b55c3b05ebc374fbbb39e4a7380f03ee65be5f2b5b7b; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=30b6a10d87ae77cce375cafda8fc3f2a%2C; passport_auth_status_ss=30b6a10d87ae77cce375cafda8fc3f2a%2C; sid_guard=76f286fcb74fe1c863d95e984c75a39e%7C1753869599%7C5184000%7CSun%2C+28-Sep-2025+09%3A59%3A59+GMT; uid_tt=f0649b32573486f603a5910e87efee82; uid_tt_ss=f0649b32573486f603a5910e87efee82; sid_tt=76f286fcb74fe1c863d95e984c75a39e; sessionid=76f286fcb74fe1c863d95e984c75a39e; sessionid_ss=76f286fcb74fe1c863d95e984c75a39e; session_tlb_tag=sttt%7C8%7CdvKG_LdP4chj2V6YTHWjnv_________P3rzelBNJ06HunzJflrwKP1i5fPvlQLpG3XaOLaXNUxU%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; ssid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC45NTM1NTE4Nzg2NjM2NzQyfQ==.+VUnRvmf8t8OAT5e64H0M4TT/JD6eqpFNXin/KSDCaQ=; i18next=zh; flow_ssr_sidebar_expand=1; ttwid=1%7CG_ugBSA7aPKN7hVyth9pnegG5dZcVXYm0c59Fglz-OY%7C1753885322%7Cf2af41accef6951c94442e89c340cbcfa9d7ae4168dc231cd0f9d3937cd9af70; passport_fe_beating_status=true; msToken=DA1QpP7aquBoqZApC0XwDQqLxAXqH22TKQ-sWbTXR2o8Rgwmv5Dp4B9vdNTahsYdDJyiLUXxlkn34O5jT1OHRitDSH2v9dOD7pc4G8IVlhFURuaxDW3s87UGU8bIl7zt18ODuv64xSmD; flow_user_country=CN; tt_scid=NsGZSlIPaqR8-nyNIGLjSEKIadRXi7V1OgR5VAGFAOn8IkrbZnfmX.vPbuZKH7Kda78a; _ga_G8EP5CG8VZ=GS2.1.s1753885333$o2$g1$t1753885337$j56$l0$h0"


class UltimateAntiLimit:
    def __init__(self):
        self.last_request_time = 0
        self.request_count = 0
        self.failed_attempts = 0
        self.base_delay = 30  # 基础延迟30秒
        
    def calculate_delay(self):
        """计算动态延迟"""
        # 基础延迟
        delay = self.base_delay
        
        # 根据失败次数增加延迟
        if self.failed_attempts > 0:
            delay += self.failed_attempts * 30  # 每次失败增加30秒
            
        # 添加随机性
        delay += random.uniform(10, 30)
        
        return delay
        
    def wait_if_needed(self):
        """智能等待"""
        current_time = time.time()
        time_diff = current_time - self.last_request_time
        
        required_delay = self.calculate_delay()
        
        if time_diff < required_delay:
            wait_time = required_delay - time_diff
            print(f"⏳ 智能延迟 {wait_time:.1f} 秒 (失败次数: {self.failed_attempts})")
            
            # 分段显示等待进度
            while wait_time > 0:
                if wait_time > 10:
                    print(f"⏰ 还需等待 {wait_time:.0f} 秒...")
                    time.sleep(10)
                    wait_time -= 10
                else:
                    time.sleep(wait_time)
                    wait_time = 0
                    
        self.last_request_time = time.time()
        self.request_count += 1


# 全局实例
ultimate_anti = UltimateAntiLimit()


def send_message_ultimate(message: str):
    """终极反限流发送消息"""
    
    # 应用智能延迟
    ultimate_anti.wait_if_needed()
    
    print(f"🚀 尝试第 {ultimate_anti.request_count} 次请求...")
    
    url = "https://www.doubao.com/samantha/chat/completion"
    
    # 最简化的参数 - 只保留必要的
    params = {
        "aid": "497858",
        "device_platform": "web",
        "language": "zh"
    }
    
    # 最简化的请求头
    headers = {
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Origin": "https://www.doubao.com",
        "Referer": "https://www.doubao.com/chat/",
        "Cookie": COOKIE
    }
    
    # 最简化的payload
    payload = {
        "messages": [
            {
                "content": json.dumps({"text": message}),
                "content_type": 2001
            }
        ],
        "completion_option": {
            "is_regen": False,
            "with_suggest": True,
            "stream": True
        }
    }
    
    try:
        with httpx.Client(timeout=60.0) as client:
            response = client.post(url, params=params, headers=headers, json=payload)
            print(f"📡 状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ 请求失败: {response.text}")
                ultimate_anti.failed_attempts += 1
                return None
            
            # 流式处理
            with client.stream("POST", url, params=params, headers=headers, json=payload) as stream:
                if stream.status_code != 200:
                    ultimate_anti.failed_attempts += 1
                    return None
                
                print("📺 接收响应...")
                full_text = ""
                
                for line in stream.iter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]
                        if not data_str.strip():
                            continue
                            
                        try:
                            data = json.loads(data_str)
                            event_type = data.get("event_type")
                            event_data = data.get("event_data", "{}")
                            
                            if event_type == 2005:  # 错误
                                error_obj = json.loads(event_data)
                                error_code = error_obj.get("code")
                                
                                if error_code == 710022004:
                                    print("⚠️ 仍被限流")
                                    ultimate_anti.failed_attempts += 1
                                    # 大幅增加延迟
                                    ultimate_anti.base_delay = min(ultimate_anti.base_delay * 1.5, 300)
                                    print(f"🔧 调整基础延迟为 {ultimate_anti.base_delay:.0f} 秒")
                                    return None
                                else:
                                    print(f"❌ 其他错误: {error_obj.get('message')}")
                                    ultimate_anti.failed_attempts += 1
                                    return None
                                    
                            elif event_type == 2001:  # 消息
                                event_obj = json.loads(event_data)
                                message_data = event_obj.get("message", {})
                                content = message_data.get("content", "{}")
                                
                                if content != "{}":
                                    content_obj = json.loads(content)
                                    text = content_obj.get("text", "")
                                    if text:
                                        print(text, end="", flush=True)
                                        full_text += text
                                        
                        except json.JSONDecodeError:
                            continue
                
                # 成功了，重置失败计数
                if full_text:
                    ultimate_anti.failed_attempts = 0
                    ultimate_anti.base_delay = max(ultimate_anti.base_delay * 0.8, 30)  # 逐渐减少延迟
                    print(f"\n✅ 成功! 调整延迟为 {ultimate_anti.base_delay:.0f} 秒")
                
                print()
                return full_text
                
    except Exception as e:
        print(f"❌ 异常: {e}")
        ultimate_anti.failed_attempts += 1
        return None


def test_ultimate_strategy():
    """测试终极策略"""
    print("🛡️ 豆包终极反限流测试")
    print("=" * 50)
    print("🔧 策略特点:")
    print("  - 动态延迟调整 (30秒起步)")
    print("  - 失败自适应 (每次失败+30秒)")
    print("  - 成功后逐渐减少延迟")
    print("  - 最简化请求参数")
    print()
    
    test_messages = [
        "你好",
        "今天天气怎么样？",
        "请介绍一下自己"
    ]
    
    for i, msg in enumerate(test_messages, 1):
        print(f"📝 测试消息 {i}: {msg}")
        print("-" * 30)
        
        response = send_message_ultimate(msg)
        
        if response:
            print(f"✅ 成功! 回复长度: {len(response)} 字符")
        else:
            print("❌ 失败")
            
        print()
        
        # 如果不是最后一条消息，询问是否继续
        if i < len(test_messages):
            choice = input("继续下一条测试? (y/n): ")
            if choice.lower() != 'y':
                break


def interactive_ultimate():
    """交互模式"""
    print("🤖 豆包终极反限流聊天")
    print("💡 自动调整延迟策略")
    print("📝 输入 'quit' 退出")
    print("-" * 40)
    
    while True:
        try:
            message = input("\n您: ").strip()
            if message.lower() in ['quit', 'exit', '退出']:
                print("👋 再见!")
                break
            
            if not message:
                continue
                
            print("豆包: ", end="", flush=True)
            response = send_message_ultimate(message)
            
            if not response:
                print("❌ 请求失败")
                choice = input("是否继续? (y/n): ")
                if choice.lower() != 'y':
                    break
                    
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "chat":
        interactive_ultimate()
    else:
        test_ultimate_strategy()
