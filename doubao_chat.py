#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包聊天API客户端 - 基于抓包信息
使用方法：
1. 先从浏览器获取最新的Cookie和参数
2. 替换下面的Cookie和参数值
3. 运行脚本
"""

import httpx
import json


# 配置区域 - 需要更新的参数
COOKIE = "hook_slardar_session_id=2025073017592839920BEBA2D613EF2863,ttwid=1%7CiMFHx1Da_BetezZn05zfft9t1j-kjns3qTR_5L6OV08%7C1753869568%7Ca4235a632c824c9806738adf2a81d8f6c10d14bc68364c986d1de738467b554c; _ga=GA1.1.*********.1753869579; ttcid=680f850c9d6c4cef8c2896d7eb66eabc38; s_v_web_id=verify_mdpso4wy_jIHunFlc_ubrJ_4rnM_AFJ2_lnMQE6tXGixX; passport_csrf_token=d24a324fc83e183d82d2ff376865d10a; passport_csrf_token_default=d24a324fc83e183d82d2ff376865d10a; passport_mfa_token=CjhmMmxq74s45voqX5p6YdkTf1aOmqKOzpFl16J07%2BVgDkKOQQEZLdf9yskI1i%2BDekA5EUy5DoOLVBpKCjwAAAAAAAAAAAAAT0vzn5wH%2F%2BIeVMweyqaIHWGbns9B9a58PfbsvTRkyotTHxZwlgjMUlSxPv1wGWBuNS0Q1474DRj2sdFsIAIiAQOsujFr; d_ticket=560ba6c243be3cc7d1a5ef686b58aeaac2adc; odin_tt=92daead88f19c6eeeddec32e138fe8b384de6e89757f7daecc7e9047933659c6fbd9ccfa1aaf65e89782b55c3b05ebc374fbbb39e4a7380f03ee65be5f2b5b7b; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=30b6a10d87ae77cce375cafda8fc3f2a%2C; passport_auth_status_ss=30b6a10d87ae77cce375cafda8fc3f2a%2C; sid_guard=76f286fcb74fe1c863d95e984c75a39e%7C1753869599%7C5184000%7CSun%2C+28-Sep-2025+09%3A59%3A59+GMT; uid_tt=f0649b32573486f603a5910e87efee82; uid_tt_ss=f0649b32573486f603a5910e87efee82; sid_tt=76f286fcb74fe1c863d95e984c75a39e; sessionid=76f286fcb74fe1c863d95e984c75a39e; sessionid_ss=76f286fcb74fe1c863d95e984c75a39e; session_tlb_tag=sttt%7C8%7CdvKG_LdP4chj2V6YTHWjnv_________P3rzelBNJ06HunzJflrwKP1i5fPvlQLpG3XaOLaXNUxU%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; ssid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC45NTM1NTE4Nzg2NjM2NzQyfQ==.+VUnRvmf8t8OAT5e64H0M4TT/JD6eqpFNXin/KSDCaQ=; i18next=zh; flow_ssr_sidebar_expand=1; ttwid=1%7CG_ugBSA7aPKN7hVyth9pnegG5dZcVXYm0c59Fglz-OY%7C1753885322%7Cf2af41accef6951c94442e89c340cbcfa9d7ae4168dc231cd0f9d3937cd9af70; passport_fe_beating_status=true; msToken=DA1QpP7aquBoqZApC0XwDQqLxAXqH22TKQ-sWbTXR2o8Rgwmv5Dp4B9vdNTahsYdDJyiLUXxlkn34O5jT1OHRitDSH2v9dOD7pc4G8IVlhFURuaxDW3s87UGU8bIl7zt18ODuv64xSmD; flow_user_country=CN; tt_scid=NsGZSlIPaqR8-nyNIGLjSEKIadRXi7V1OgR5VAGFAOn8IkrbZnfmX.vPbuZKH7Kda78a; _ga_G8EP5CG8VZ=GS2.1.s1753885333$o2$g1$t1753885337$j56$l0$h0"


def send_chat_message(message: str, cookie: str = None, debug: bool = False):
    """发送聊天消息到豆包API"""

    if debug:
        print("🔍 调试模式开启")
        print(f"请求时间: {__import__('datetime').datetime.now()}")
        print(f"消息长度: {len(message)} 字符")

    print("正在发送请求...")

    # 完全按照抓包信息构建请求
    url = "https://www.doubao.com/samantha/chat/completion"

    # URL参数 - 从抓包信息中获取（移除msToken和a_bogus）
    params = {
        "aid": "497858",
        "device_id": "7532812407958914560",
        "device_platform": "web",
        "language": "zh",
        "pc_version": "2.29.3",
        "pkg_type": "release_version",
        "real_aid": "497858",
        "region": "CN",
        "samantha_web": "1",
        "sys_region": "CN",
        "tea_uuid": "7532812413725918772",
        "use-olympus-account": "1",
        "version_code": "20800",
        "web_id": "7532812413725918772"
    }

    # 请求头 - 按照抓包信息完整还原
    headers = {
        "Host": "www.doubao.com",
        "Connection": "keep-alive",
        "x-flow-trace": "04-001b75fc14a09438001a82b8a36dedb7-001041b7f0b52afe-01",
        "sec-ch-ua-platform": '"Linux"',
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "Agw-Js-Conv": "str, str",
        "last-event-id": "undefined",
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/138.0.3351.83",
        "content-type": "application/json",
        "Accept": "*/*",
        "Origin": "https://www.doubao.com",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Referer": "https://www.doubao.com/chat/",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cookie": cookie or COOKIE
    }

    # 请求体数据 - 完全按照抓包信息
    payload = {
        "messages": [
            {
                "content": json.dumps({"text": message}),
                "content_type": 2001,
                "attachments": [],
                "references": []
            }
        ],
        "completion_option": {
            "is_regen": False,
            "with_suggest": True,
            "need_create_conversation": True,
            "launch_stage": 1,
            "is_replace": False,
            "is_delete": False,
            "message_from": 0,
            "use_deep_think": False,
            "use_auto_cot": True,
            "resend_for_regen": False,
            "event_id": "0"
        },
        "evaluate_option": {
            "web_ab_params": ""
        },
        "conversation_id": "0",
        "local_conversation_id": "local_8518976236113172",
        "local_message_id": "a77d8dc0-6d50-11f0-aeb7-2757bc9d181e"
    }

    # 发送流式请求并处理响应
    try:
        with httpx.Client(timeout=30.0) as client:
            with client.stream("POST", url, params=params, headers=headers, json=payload) as response:
                print(f"响应状态码: {response.status_code}")

                if response.status_code != 200:
                    print(f"请求失败: {response.status_code}")
                    print(f"响应内容: {response.text}")
                    return None

                print("开始接收流式响应...")
                full_text = ""
                line_count = 0

                for line in response.iter_lines():
                    line_count += 1

                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除 "data: " 前缀

                        if data_str.strip() == "":
                            continue

                        try:
                            data = json.loads(data_str)
                            event_data = data.get("event_data", "{}")
                            event_type = data.get("event_type")

                            # 检查错误信息
                            if event_type == 2005:  # 错误事件
                                try:
                                    error_obj = json.loads(event_data)
                                    error_code = error_obj.get("code")
                                    error_message = error_obj.get("message", "")
                                    error_detail = error_obj.get("error_detail", {})

                                    if error_code == 710022004:
                                        print("❌ 请求被限流")
                                        print("📊 限流分析:")
                                        print("  - 可能原因: 请求频率过高")
                                        print("  - 建议等待: 5-10分钟")
                                        print("  - 或尝试: 在浏览器中正常聊天几次")
                                        if debug:
                                            print(f"  - 错误详情: {error_detail}")
                                        return None
                                    else:
                                        print(f"❌ 服务器错误: {error_message} (代码: {error_code})")
                                        if debug:
                                            print(f"错误详情: {error_detail}")
                                        return None
                                except:
                                    print("❌ 服务器返回错误")
                                    return None

                            elif event_type == 2001:  # 消息事件
                                event_obj = json.loads(event_data)
                                message_data = event_obj.get("message", {})
                                content = message_data.get("content", "{}")

                                if content != "{}":
                                    content_obj = json.loads(content)
                                    text = content_obj.get("text", "")
                                    if text:
                                        print(text, end="", flush=True)
                                        full_text += text

                        except json.JSONDecodeError as e:
                            # 忽略JSON解析错误，继续处理下一行
                            continue

                print()  # 换行
                return full_text

    except httpx.HTTPStatusError as e:
        print(f"HTTP错误: {e}")
        if hasattr(e, 'response'):
            print(f"响应内容: {e.response.text}")
        return None
    except Exception as e:
        print(f"请求异常: {e}")
        return None


def update_cookie(new_cookie: str):
    """更新Cookie"""
    global COOKIE
    COOKIE = new_cookie
    print("✅ Cookie已更新")


def send_chat_with_retry(message: str, max_retries: int = 3, delay: int = 5):
    """发送消息，支持重试"""
    import time

    for attempt in range(max_retries):
        print(f"尝试第 {attempt + 1} 次...")
        response = send_chat_message(message)

        if response:
            return response

        if attempt < max_retries - 1:
            print(f"等待 {delay} 秒后重试...")
            time.sleep(delay)

    print("❌ 重试次数已用完")
    return None


def interactive_chat():
    """交互式聊天"""
    print("🤖 豆包聊天模式 (输入 'quit' 退出)")
    print("-" * 40)

    while True:
        try:
            message = input("\n您: ").strip()
            if message.lower() in ['quit', 'exit', '退出']:
                print("👋 再见!")
                break

            if not message:
                continue

            print("豆包: ", end="", flush=True)
            response = send_chat_message(message)

            if not response:
                print("❌ 请求失败，请检查Cookie是否有效")

        except KeyboardInterrupt:
            print("\n👋 再见!")
            break


def main():
    """主函数"""
    print("=" * 50)
    print("豆包聊天API客户端")
    print("=" * 50)
    print()
    print("使用说明:")
    print("1. 如果Cookie过期，请更新脚本顶部的COOKIE变量")
    print("2. 或者调用 update_cookie('新的cookie值') 函数")
    print("3. 可以调用 interactive_chat() 进行交互式聊天")
    print()

    # 测试消息
    message = "现在北京时间几点了"
    print(f"测试消息: {message}")
    print("-" * 30)

    response = send_chat_message(message)

    if response:
        print(f"\n✅ 请求成功!")
        print(f"完整回复: {response}")
        print("\n💡 提示: 可以调用 interactive_chat() 开始交互式聊天")
    else:
        print(f"\n❌ 请求失败!")
        print("常见问题及解决方法:")
        print("1. 限流错误: 等待几分钟后重试")
        print("2. Cookie过期: 从浏览器重新获取Cookie")
        print("3. 网络问题: 检查网络连接")
        print("\n获取新Cookie的方法:")
        print("- 打开浏览器，登录豆包")
        print("- 开发者工具(F12) -> Network -> 发送消息")
        print("- 找到chat/completion请求，复制Cookie")


if __name__ == "__main__":
    main()
