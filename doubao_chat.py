#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包聊天API客户端
基于抓包信息实现的豆包聊天接口调用
"""

import httpx
import json
import asyncio
from typing import AsyncGenerator


class DoubaoChat:
    def __init__(self):
        self.base_url = "https://www.doubao.com"
        self.headers = {
            "Host": "www.doubao.com",
            "Connection": "keep-alive",
            "x-flow-trace": "04-001b75fc14a09438001a82b8a36dedb7-001041b7f0b52afe-01",
            "sec-ch-ua-platform": '"Linux"',
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "sec-ch-ua-mobile": "?0",
            "Agw-Js-Conv": "str, str",
            "last-event-id": "undefined",
            "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/138.0.3351.83",
            "content-type": "application/json",
            "Accept": "*/*",
            "Origin": "https://www.doubao.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://www.doubao.com/chat/",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cookie": "hook_slardar_session_id=2025073017592839920BEBA2D613EF2863,ttwid=1%7CiMFHx1Da_BetezZn05zfft9t1j-kjns3qTR_5L6OV08%7C1753869568%7Ca4235a632c824c9806738adf2a81d8f6c10d14bc68364c986d1de738467b554c; _ga=GA1.1.*********.1753869579; ttcid=680f850c9d6c4cef8c2896d7eb66eabc38; s_v_web_id=verify_mdpso4wy_jIHunFlc_ubrJ_4rnM_AFJ2_lnMQE6tXGixX; passport_csrf_token=d24a324fc83e183d82d2ff376865d10a; passport_csrf_token_default=d24a324fc83e183d82d2ff376865d10a; passport_mfa_token=CjhmMmxq74s45voqX5p6YdkTf1aOmqKOzpFl16J07%2BVgDkKOQQEZLdf9yskI1i%2BDekA5EUy5DoOLVBpKCjwAAAAAAAAAAAAAT0vzn5wH%2F%2BIeVMweyqaIHWGbns9B9a58PfbsvTRkyotTHxZwlgjMUlSxPv1wGWBuNS0Q1474DRj2sdFsIAIiAQOsujFr; d_ticket=560ba6c243be3cc7d1a5ef686b58aeaac2adc; odin_tt=92daead88f19c6eeeddec32e138fe8b384de6e89757f7daecc7e9047933659c6fbd9ccfa1aaf65e89782b55c3b05ebc374fbbb39e4a7380f03ee65be5f2b5b7b; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=30b6a10d87ae77cce375cafda8fc3f2a%2C; passport_auth_status_ss=30b6a10d87ae77cce375cafda8fc3f2a%2C; sid_guard=76f286fcb74fe1c863d95e984c75a39e%7C1753869599%7C5184000%7CSun%2C+28-Sep-2025+09%3A59%3A59+GMT; uid_tt=f0649b32573486f603a5910e87efee82; uid_tt_ss=f0649b32573486f603a5910e87efee82; sid_tt=76f286fcb74fe1c863d95e984c75a39e; sessionid=76f286fcb74fe1c863d95e984c75a39e; sessionid_ss=76f286fcb74fe1c863d95e984c75a39e; session_tlb_tag=sttt%7C8%7CdvKG_LdP4chj2V6YTHWjnv_________P3rzelBNJ06HunzJflrwKP1i5fPvlQLpG3XaOLaXNUxU%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; ssid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC45NTM1NTE4Nzg2NjM2NzQyfQ==.+VUnRvmf8t8OAT5e64H0M4TT/JD6eqpFNXin/KSDCaQ=; i18next=zh; flow_ssr_sidebar_expand=1; ttwid=1%7CG_ugBSA7aPKN7hVyth9pnegG5dZcVXYm0c59Fglz-OY%7C1753885322%7Cf2af41accef6951c94442e89c340cbcfa9d7ae4168dc231cd0f9d3937cd9af70; passport_fe_beating_status=true; msToken=DA1QpP7aquBoqZApC0XwDQqLxAXqH22TKQ-sWbTXR2o8Rgwmv5Dp4B9vdNTahsYdDJyiLUXxlkn34O5jT1OHRitDSH2v9dOD7pc4G8IVlhFURuaxDW3s87UGU8bIl7zt18ODuv64xSmD; flow_user_country=CN; tt_scid=NsGZSlIPaqR8-nyNIGLjSEKIadRXi7V1OgR5VAGFAOn8IkrbZnfmX.vPbuZKH7Kda78a; _ga_G8EP5CG8VZ=GS2.1.s1753885333$o2$g1$t1753885337$j56$l0$h0"
        }
        
        # 请求参数
        self.params = {
            "aid": "497858",
            "device_id": "7532812407958914560",
            "device_platform": "web",
            "language": "zh",
            "pc_version": "2.29.3",
            "pkg_type": "release_version",
            "real_aid": "497858",
            "region": "CN",
            "samantha_web": "1",
            "sys_region": "CN",
            "tea_uuid": "7532812413725918772",
            "use-olympus-account": "1",
            "version_code": "20800",
            "web_id": "7532812413725918772",
            "msToken": "6l0gdLOzJzKfjV1v-XnJqtP5aVUqIi0WA1e8CGf04WG0T660r4HnH6C40yCRF3MxqYyNHgGMXyaKNN-ybnN6eM3VZA93dippCu4Tjj9SISlrxWl9Ep2_G4gtqx1SY-nBLIcUoG41vgWP-Y9TXKmrNyfPRVo5a2yoPuEquSJHtSvmN2K_nsbZkw%3D%3D",
            "a_bogus": "EvsnDeW7E2m5a3Ct8Opj9fqRU0AMrsuyUaiKRYpR9KOWcZzTF%2Fl5hPrXJFz50BC488kTiFx7TE0bGdEOs8R2U1npKmhku%2FvRK4IAVUsoMqZmGPvhrNS%2Fe74FqhMzUSGYzcCyNVEXAt1ohEq-qrawUQVrt%2FEK-cRsMHVY4p0LrNn8D8UcoNHAKrfpOXirQ524WE%3D%3D"
        }

    def create_payload(self, message: str, conversation_id: str = "0", local_conversation_id: str = "local_8518976236113172"):
        """创建请求载荷"""
        return {
            "messages": [
                {
                    "content": json.dumps({"text": message}),
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                }
            ],
            "completion_option": {
                "is_regen": False,
                "with_suggest": True,
                "need_create_conversation": True,
                "launch_stage": 1,
                "is_replace": False,
                "is_delete": False,
                "message_from": 0,
                "use_deep_think": False,
                "use_auto_cot": True,
                "resend_for_regen": False,
                "event_id": "0"
            },
            "evaluate_option": {
                "web_ab_params": ""
            },
            "conversation_id": conversation_id,
            "local_conversation_id": local_conversation_id,
            "local_message_id": "a77d8dc0-6d50-11f0-aeb7-2757bc9d181e"
        }

    async def chat_stream(self, message: str) -> AsyncGenerator[str, None]:
        """发送聊天消息并流式接收响应"""
        url = f"{self.base_url}/samantha/chat/completion"
        payload = self.create_payload(message)
        
        async with httpx.AsyncClient() as client:
            async with client.stream(
                "POST",
                url,
                params=self.params,
                headers=self.headers,
                json=payload,
                timeout=30.0
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除 "data: " 前缀
                        if data_str.strip() == "":
                            continue
                            
                        try:
                            data = json.loads(data_str)
                            event_data = data.get("event_data", "{}")
                            event_type = data.get("event_type")
                            
                            if event_type == 2001:  # 消息事件
                                event_obj = json.loads(event_data)
                                message_data = event_obj.get("message", {})
                                content = message_data.get("content", "{}")
                                
                                if content != "{}":
                                    content_obj = json.loads(content)
                                    text = content_obj.get("text", "")
                                    if text:
                                        yield text
                                        
                        except json.JSONDecodeError:
                            continue

    async def chat(self, message: str) -> str:
        """发送聊天消息并获取完整响应"""
        full_response = ""
        async for chunk in self.chat_stream(message):
            full_response += chunk
        return full_response


async def main():
    """主函数示例"""
    chat_client = DoubaoChat()
    
    # 示例1: 流式聊天
    print("=== 流式聊天示例 ===")
    message = "你是谁"
    print(f"用户: {message}")
    print("豆包: ", end="", flush=True)
    
    async for chunk in chat_client.chat_stream(message):
        print(chunk, end="", flush=True)
    print("\n")
    
    # 示例2: 获取完整响应
    print("=== 完整响应示例 ===")
    message = "介绍一下Python"
    print(f"用户: {message}")
    response = await chat_client.chat(message)
    print(f"豆包: {response}")


if __name__ == "__main__":
    asyncio.run(main())
