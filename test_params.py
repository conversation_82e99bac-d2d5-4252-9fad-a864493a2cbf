import httpx
import json

# 从原脚本导入Cookie
from doubao_chat import COOKIE

def test_different_params():
    """测试不同参数组合"""

    url = "https://www.doubao.com/samantha/chat/completion"

    # 测试1: 最小参数集
    print("🧪 测试1: 最小参数集")
    test_request({}, {
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
        "Content-Type": "application/json",
        "Cookie": COOKIE
    })

    # 测试2: 添加基本参数
    print("\n🧪 测试2: 基本参数")
    test_request({
        "aid": "497858",
        "device_platform": "web",
        "language": "zh"
    }, {
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
        "Content-Type": "application/json",
        "Origin": "https://www.doubao.com",
        "Referer": "https://www.doubao.com/chat/",
        "Cookie": COOKIE
    })

    # 测试3: 完整原始参数（从抓包）
    print("\n🧪 测试3: 完整原始参数")
    test_request({
        "aid": "497858",
        "device_id": "7532812407958914560",
        "device_platform": "web",
        "language": "zh",
        "pc_version": "2.29.3",
        "pkg_type": "release_version",
        "real_aid": "497858",
        "region": "CN",
        "samantha_web": "1",
        "sys_region": "CN",
        "tea_uuid": "7532812413725918772",
        "use-olympus-account": "1",
        "version_code": "20800",
        "web_id": "7532812413725918772"
    }, {
        "Host": "www.doubao.com",
        "Connection": "keep-alive",
        "x-flow-trace": "04-001b75fc14a09438001a82b8a36dedb7-001041b7f0b52afe-01",
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/138.0.3351.83",
        "content-type": "application/json",
        "Accept": "*/*",
        "Origin": "https://www.doubao.com",
        "Referer": "https://www.doubao.com/chat/",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cookie": COOKIE
    })

def test_request(params, headers):
    """测试单个请求"""

    url = "https://www.doubao.com/samantha/chat/completion"
    payload = {
        "messages": [
            {
                "content": json.dumps({"text": "测试"}),
                "content_type": 2001
            }
        ],
        "completion_option": {
            "is_regen": False,
            "with_suggest": True,
            "stream": True
        }
    }

    try:
        with httpx.Client(timeout=10.0) as client:
            response = client.post(url, params=params, headers=headers, json=payload)
            print(f"  状态码: {response.status_code}")

            if response.status_code == 200:
                print("  ✅ 成功!")
            else:
                print(f"  ❌ 失败: {response.status_code}")

    except Exception as e:
        print(f"  ❌ 异常: {e}")

if __name__ == "__main__":
    test_different_params()