import httpx
import json
import time


# 配置
COOKIE = "hook_slardar_session_id=2025073017592839920BEBA2D613EF2863,ttwid=1%7CiMFHx1Da_BetezZn05zfft9t1j-kjns3qTR_5L6OV08%7C1753869568%7Ca4235a632c824c9806738adf2a81d8f6c10d14bc68364c986d1de738467b554c; _ga=GA1.1.*********.1753869579; ttcid=680f850c9d6c4cef8c2896d7eb66eabc38; s_v_web_id=verify_mdpso4wy_jIHunFlc_ubrJ_4rnM_AFJ2_lnMQE6tXGixX; passport_csrf_token=d24a324fc83e183d82d2ff376865d10a; passport_csrf_token_default=d24a324fc83e183d82d2ff376865d10a; passport_mfa_token=CjhmMmxq74s45voqX5p6YdkTf1aOmqKOzpFl16J07%2BVgDkKOQQEZLdf9yskI1i%2BDekA5EUy5DoOLVBpKCjwAAAAAAAAAAAAAT0vzn5wH%2F%2BIeVMweyqaIHWGbns9B9a58PfbsvTRkyotTHxZwlgjMUlSxPv1wGWBuNS0Q1474DRj2sdFsIAIiAQOsujFr; d_ticket=560ba6c243be3cc7d1a5ef686b58aeaac2adc; odin_tt=92daead88f19c6eeeddec32e138fe8b384de6e89757f7daecc7e9047933659c6fbd9ccfa1aaf65e89782b55c3b05ebc374fbbb39e4a7380f03ee65be5f2b5b7b; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=30b6a10d87ae77cce375cafda8fc3f2a%2C; passport_auth_status_ss=30b6a10d87ae77cce375cafda8fc3f2a%2C; sid_guard=76f286fcb74fe1c863d95e984c75a39e%7C1753869599%7C5184000%7CSun%2C+28-Sep-2025+09%3A59%3A59+GMT; uid_tt=f0649b32573486f603a5910e87efee82; uid_tt_ss=f0649b32573486f603a5910e87efee82; sid_tt=76f286fcb74fe1c863d95e984c75a39e; sessionid=76f286fcb74fe1c863d95e984c75a39e; sessionid_ss=76f286fcb74fe1c863d95e984c75a39e; session_tlb_tag=sttt%7C8%7CdvKG_LdP4chj2V6YTHWjnv_________P3rzelBNJ06HunzJflrwKP1i5fPvlQLpG3XaOLaXNUxU%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; ssid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC45NTM1NTE4Nzg2NjM2NzQyfQ==.+VUnRvmf8t8OAT5e64H0M4TT/JD6eqpFNXin/KSDCaQ=; i18next=zh; flow_ssr_sidebar_expand=1; ttwid=1%7CG_ugBSA7aPKN7hVyth9pnegG5dZcVXYm0c59Fglz-OY%7C1753885322%7Cf2af41accef6951c94442e89c340cbcfa9d7ae4168dc231cd0f9d3937cd9af70; passport_fe_beating_status=true; msToken=DA1QpP7aquBoqZApC0XwDQqLxAXqH22TKQ-sWbTXR2o8Rgwmv5Dp4B9vdNTahsYdDJyiLUXxlkn34O5jT1OHRitDSH2v9dOD7pc4G8IVlhFURuaxDW3s87UGU8bIl7zt18ODuv64xSmD; flow_user_country=CN; tt_scid=NsGZSlIPaqR8-nyNIGLjSEKIadRXi7V1OgR5VAGFAOn8IkrbZnfmX.vPbuZKH7Kda78a; _ga_G8EP5CG8VZ=GS2.1.s1753885333$o2$g1$t1753885337$j56$l0$h0"


def send_message_fixed(message: str, debug: bool = False):
    """修复版本 - 基于诊断结果优化的流式处理"""
    
    print(f"🚀 发送消息: {message}")
    
    url = "https://www.doubao.com/samantha/chat/completion"
    
    # 最简化但完整的payload
    payload = {
        "messages": [
            {
                "content": json.dumps({"text": message}),
                "content_type": 2001
            }
        ],
        "completion_option": {
            "stream": True
        }
    }
    
    headers = {
        "Content-Type": "application/json",
        "Cookie": COOKIE
    }
    
    try:
        with httpx.Client(timeout=30.0) as client:
            # 直接发送流式请求
            with client.stream("POST", url, json=payload, headers=headers) as response:
                print(f"📡 状态码: {response.status_code}")
                
                if response.status_code != 200:
                    print(f"❌ 请求失败: {response.status_code}")
                    # 读取错误响应
                    error_content = ""
                    for chunk in response.iter_text():
                        error_content += chunk
                    print(f"错误内容: {error_content[:500]}...")
                    return None
                
                print("📺 开始接收流式响应...")
                full_response = ""
                message_text = ""
                
                # 逐行处理流式响应
                for line in response.iter_lines():
                    if debug:
                        print(f"[DEBUG] 原始行: {line}")
                    
                    # 处理SSE格式
                    if line.startswith("data: "):
                        data_content = line[6:]  # 移除 "data: " 前缀
                        
                        # 跳过空行
                        if not data_content.strip():
                            continue
                        
                        try:
                            # 解析JSON数据
                            data = json.loads(data_content)
                            event_type = data.get("event_type")
                            event_data = data.get("event_data", "{}")
                            
                            if debug:
                                print(f"[DEBUG] 事件类型: {event_type}")
                            
                            # 处理不同类型的事件
                            if event_type == 2001:  # 消息事件
                                try:
                                    event_obj = json.loads(event_data)
                                    message_data = event_obj.get("message", {})
                                    content = message_data.get("content", "{}")
                                    
                                    if content and content != "{}":
                                        content_obj = json.loads(content)
                                        text = content_obj.get("text", "")
                                        
                                        if text:
                                            print(text, end="", flush=True)
                                            message_text += text
                                            
                                except (json.JSONDecodeError, KeyError) as e:
                                    if debug:
                                        print(f"[DEBUG] 消息解析错误: {e}")
                                    continue
                                    
                            elif event_type == 2005:  # 错误事件
                                try:
                                    error_obj = json.loads(event_data)
                                    error_code = error_obj.get("code")
                                    error_message = error_obj.get("message", "")
                                    
                                    print(f"\n❌ 服务器错误: {error_message} (代码: {error_code})")
                                    
                                    if error_code == 710022004:
                                        print("⚠️ 这是限流错误，建议等待后重试")
                                    
                                    return None
                                    
                                except (json.JSONDecodeError, KeyError) as e:
                                    print(f"\n❌ 错误解析失败: {e}")
                                    return None
                            
                            elif event_type == 2003:  # 结束事件
                                if debug:
                                    print(f"\n[DEBUG] 响应结束")
                                break
                                
                            else:
                                if debug:
                                    print(f"[DEBUG] 未知事件类型: {event_type}")
                        
                        except json.JSONDecodeError as e:
                            if debug:
                                print(f"[DEBUG] JSON解析错误: {e}, 数据: {data_content[:100]}...")
                            continue
                
                print()  # 换行
                return message_text
                
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None


def interactive_chat_fixed():
    """交互式聊天 - 修复版本"""
    print("🤖 豆包聊天 - 修复版本")
    print("💡 基于诊断结果优化的流式处理")
    print("📝 输入 'quit' 退出, 'debug' 切换调试模式")
    print("-" * 50)
    
    debug_mode = False
    
    while True:
        try:
            user_input = input("\n您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见!")
                break
            elif user_input.lower() == 'debug':
                debug_mode = not debug_mode
                print(f"🔧 调试模式: {'开启' if debug_mode else '关闭'}")
                continue
            elif not user_input:
                continue
            
            print("豆包: ", end="", flush=True)
            response = send_message_fixed(user_input, debug=debug_mode)
            
            if not response:
                print("❌ 请求失败")
                choice = input("是否继续? (y/n): ")
                if choice.lower() != 'y':
                    break
                    
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break


def test_fixed_version():
    """测试修复版本"""
    print("🧪 测试修复版本")
    print("=" * 30)
    
    test_messages = [
        "你好",
        "今天是几号？",
        "请简单介绍一下自己"
    ]
    
    for i, msg in enumerate(test_messages, 1):
        print(f"\n📝 测试 {i}: {msg}")
        print("-" * 20)
        
        response = send_message_fixed(msg)
        
        if response:
            print(f"\n✅ 成功! 回复长度: {len(response)} 字符")
        else:
            print("\n❌ 失败")
            break
        
        # 间隔一下
        if i < len(test_messages):
            print("⏳ 等待3秒...")
            time.sleep(3)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "chat":
            interactive_chat_fixed()
        elif sys.argv[1] == "test":
            test_fixed_version()
    else:
        print("🛠️ 豆包聊天API - 修复版本")
        print("=" * 40)
        print("基于诊断结果的优化:")
        print("  ✅ 简化payload结构")
        print("  ✅ 优化流式响应处理")
        print("  ✅ 改进错误检测")
        print("  ✅ 增强调试功能")
        print()
        print("使用方法:")
        print("  python doubao_fixed.py test    # 运行测试")
        print("  python doubao_fixed.py chat    # 交互聊天")
        print()
        
        # 快速测试
        print("🚀 快速测试...")
        response = send_message_fixed("你好，请问你是谁？")
        
        if response:
            print(f"\n🎉 修复成功! 豆包正常响应了!")
            print(f"回复长度: {len(response)} 字符")
            print("\n💬 现在可以使用:")
            print("  python doubao_fixed.py chat")
        else:
            print("\n😔 仍有问题，可能需要:")
            print("  1. 等待更长时间")
            print("  2. 更新Cookie")
            print("  3. 检查网络连接")
