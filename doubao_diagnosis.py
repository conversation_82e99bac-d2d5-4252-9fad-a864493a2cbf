import httpx
import json
import time
from datetime import datetime


# 配置
COOKIE = "hook_slardar_session_id=2025073017592839920BEBA2D613EF2863,ttwid=1%7CiMFHx1Da_BetezZn05zfft9t1j-kjns3qTR_5L6OV08%7C1753869568%7Ca4235a632c824c9806738adf2a81d8f6c10d14bc68364c986d1de738467b554c; _ga=GA1.1.*********.1753869579; ttcid=680f850c9d6c4cef8c2896d7eb66eabc38; s_v_web_id=verify_mdpso4wy_jIHunFlc_ubrJ_4rnM_AFJ2_lnMQE6tXGixX; passport_csrf_token=d24a324fc83e183d82d2ff376865d10a; passport_csrf_token_default=d24a324fc83e183d82d2ff376865d10a; passport_mfa_token=CjhmMmxq74s45voqX5p6YdkTf1aOmqKOzpFl16J07%2BVgDkKOQQEZLdf9yskI1i%2BDekA5EUy5DoOLVBpKCjwAAAAAAAAAAAAAT0vzn5wH%2F%2BIeVMweyqaIHWGbns9B9a58PfbsvTRkyotTHxZwlgjMUlSxPv1wGWBuNS0Q1474DRj2sdFsIAIiAQOsujFr; d_ticket=560ba6c243be3cc7d1a5ef686b58aeaac2adc; odin_tt=92daead88f19c6eeeddec32e138fe8b384de6e89757f7daecc7e9047933659c6fbd9ccfa1aaf65e89782b55c3b05ebc374fbbb39e4a7380f03ee65be5f2b5b7b; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=30b6a10d87ae77cce375cafda8fc3f2a%2C; passport_auth_status_ss=30b6a10d87ae77cce375cafda8fc3f2a%2C; sid_guard=76f286fcb74fe1c863d95e984c75a39e%7C1753869599%7C5184000%7CSun%2C+28-Sep-2025+09%3A59%3A59+GMT; uid_tt=f0649b32573486f603a5910e87efee82; uid_tt_ss=f0649b32573486f603a5910e87efee82; sid_tt=76f286fcb74fe1c863d95e984c75a39e; sessionid=76f286fcb74fe1c863d95e984c75a39e; sessionid_ss=76f286fcb74fe1c863d95e984c75a39e; session_tlb_tag=sttt%7C8%7CdvKG_LdP4chj2V6YTHWjnv_________P3rzelBNJ06HunzJflrwKP1i5fPvlQLpG3XaOLaXNUxU%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; ssid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC45NTM1NTE4Nzg2NjM2NzQyfQ==.+VUnRvmf8t8OAT5e64H0M4TT/JD6eqpFNXin/KSDCaQ=; i18next=zh; flow_ssr_sidebar_expand=1; ttwid=1%7CG_ugBSA7aPKN7hVyth9pnegG5dZcVXYm0c59Fglz-OY%7C1753885322%7Cf2af41accef6951c94442e89c340cbcfa9d7ae4168dc231cd0f9d3937cd9af70; passport_fe_beating_status=true; msToken=DA1QpP7aquBoqZApC0XwDQqLxAXqH22TKQ-sWbTXR2o8Rgwmv5Dp4B9vdNTahsYdDJyiLUXxlkn34O5jT1OHRitDSH2v9dOD7pc4G8IVlhFURuaxDW3s87UGU8bIl7zt18ODuv64xSmD; flow_user_country=CN; tt_scid=NsGZSlIPaqR8-nyNIGLjSEKIadRXi7V1OgR5VAGFAOn8IkrbZnfmX.vPbuZKH7Kda78a; _ga_G8EP5CG8VZ=GS2.1.s1753885333$o2$g1$t1753885337$j56$l0$h0"


def diagnose_rate_limit():
    """诊断限流问题"""
    print("🔍 豆包限流问题诊断工具")
    print("=" * 50)
    
    # 1. 检查基础连接
    print("1️⃣ 检查基础连接...")
    try:
        with httpx.Client(timeout=10.0) as client:
            response = client.get("https://www.doubao.com/")
            print(f"   ✅ 豆包主页访问: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 豆包主页访问失败: {e}")
        return
    
    # 2. 检查API端点
    print("\n2️⃣ 检查API端点...")
    url = "https://www.doubao.com/samantha/chat/completion"
    
    # 最简请求
    params = {"aid": "497858"}
    headers = {
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
        "Cookie": COOKIE
    }
    
    try:
        with httpx.Client(timeout=10.0) as client:
            response = client.get(url, params=params, headers=headers)
            print(f"   📡 API端点状态: {response.status_code}")
            if response.status_code != 200:
                print(f"   📄 响应内容: {response.text[:200]}...")
    except Exception as e:
        print(f"   ❌ API端点访问失败: {e}")
    
    # 3. 分析Cookie有效性
    print("\n3️⃣ 分析Cookie...")
    cookie_parts = COOKIE.split(';')
    important_cookies = ['sessionid', 'sid_tt', 'uid_tt']
    
    for cookie_part in cookie_parts:
        if '=' in cookie_part:
            key = cookie_part.split('=')[0].strip()
            if key in important_cookies:
                value = cookie_part.split('=')[1]
                print(f"   🔑 {key}: {value[:20]}...")
    
    # 4. 测试最小请求
    print("\n4️⃣ 测试最小请求...")
    minimal_payload = {
        "messages": [{"content": '{"text": "hi"}', "content_type": 2001}],
        "completion_option": {"stream": True}
    }
    
    headers_minimal = {
        "Content-Type": "application/json",
        "Cookie": COOKIE
    }
    
    try:
        with httpx.Client(timeout=15.0) as client:
            response = client.post(url, json=minimal_payload, headers=headers_minimal)
            print(f"   📡 最小请求状态: {response.status_code}")
            
            if response.status_code == 200:
                # 尝试读取流式响应
                print("   📺 尝试读取流式响应...")
                content = response.text
                if "event_type" in content:
                    print("   ✅ 检测到流式响应格式")
                    
                    # 查找错误码
                    if "710022004" in content:
                        print("   ⚠️ 确认限流错误码 710022004")
                    else:
                        print("   ✅ 未检测到限流错误")
                else:
                    print(f"   📄 响应内容: {content[:200]}...")
            else:
                print(f"   📄 错误响应: {response.text[:200]}...")
                
    except Exception as e:
        print(f"   ❌ 最小请求失败: {e}")
    
    # 5. 建议解决方案
    print("\n" + "=" * 50)
    print("💡 解决方案建议:")
    print()
    print("🔄 立即可尝试的方法:")
    print("   1. 等待更长时间 (建议5-10分钟)")
    print("   2. 在浏览器中正常使用豆包几次")
    print("   3. 清除浏览器缓存后重新登录")
    print()
    print("🔧 技术解决方案:")
    print("   1. 更换IP地址 (VPN/代理)")
    print("   2. 获取新的Cookie (重新登录)")
    print("   3. 模拟更真实的浏览器行为")
    print("   4. 使用浏览器自动化 (Selenium)")
    print()
    print("⏰ 时间策略:")
    print("   1. 错峰使用 (避开高峰时段)")
    print("   2. 间隔更长时间 (每次请求间隔5-10分钟)")
    print("   3. 分批次使用 (每天限制请求数量)")


def test_wait_strategy():
    """测试等待策略"""
    print("\n🕐 等待策略测试")
    print("-" * 30)
    
    wait_times = [60, 300, 600]  # 1分钟, 5分钟, 10分钟
    
    for wait_time in wait_times:
        print(f"\n⏰ 测试等待 {wait_time//60} 分钟后的效果")
        choice = input(f"是否等待 {wait_time//60} 分钟? (y/n): ")
        
        if choice.lower() == 'y':
            print(f"⏳ 等待 {wait_time//60} 分钟...")
            
            # 分段显示进度
            remaining = wait_time
            while remaining > 0:
                if remaining >= 60:
                    print(f"⏰ 还需等待 {remaining//60} 分钟...")
                    time.sleep(60)
                    remaining -= 60
                else:
                    time.sleep(remaining)
                    remaining = 0
            
            # 测试请求
            print("🧪 测试请求...")
            url = "https://www.doubao.com/samantha/chat/completion"
            payload = {
                "messages": [{"content": '{"text": "测试"}', "content_type": 2001}],
                "completion_option": {"stream": True}
            }
            headers = {"Content-Type": "application/json", "Cookie": COOKIE}
            
            try:
                with httpx.Client(timeout=15.0) as client:
                    response = client.post(url, json=payload, headers=headers)
                    print(f"📡 状态: {response.status_code}")
                    
                    if "710022004" in response.text:
                        print("❌ 仍然限流")
                    else:
                        print("✅ 可能已解除限流")
                        break
                        
            except Exception as e:
                print(f"❌ 请求失败: {e}")
        else:
            print("⏭️ 跳过此等待时间")


if __name__ == "__main__":
    diagnose_rate_limit()
    
    print("\n" + "=" * 50)
    choice = input("是否进行等待策略测试? (y/n): ")
    if choice.lower() == 'y':
        test_wait_strategy()
    
    print("\n🎯 总结:")
    print("限流问题通常需要:")
    print("1. 足够的等待时间 (5-10分钟以上)")
    print("2. 更真实的使用模式")
    print("3. 可能需要更换IP或重新登录")
    print("\n建议先在浏览器中正常使用，确认账号状态正常后再尝试脚本。")
